.cunzhi-memory

# Python 虚拟环境
venv/
env/
.venv/
.env/

# 环境变量文件（包含敏感信息）
.env
.env.local
.env.production

# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd
.Python
*.so

# 数据文件（运行时生成的文件）
data/xs.zip
data/xs_version.txt

# 下载的文件目录
files/TVBoxOSC/

# 日志文件
logs/
*.log

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# IDE 配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 敏感配置文件（如果有的话）
config/local_config.json
config/production_config.json