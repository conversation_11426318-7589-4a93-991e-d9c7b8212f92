# GitHub配置环境变量示例
# 复制此文件为 .env 并填入实际值

# GitHub仓库信息
GITHUB_OWNER=your-username
GITHUB_REPO=your-repo-name
GITHUB_BRANCH=main

# GitHub Personal Access Token
# 在 https://github.com/settings/tokens 创建
# 需要 repo 权限
GITHUB_TOKEN=your-github-token

# 可选配置
GITHUB_API_TIMEOUT=30
LOG_LEVEL=INFO

# 使用说明：
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 编辑 .env 文件，填入实际的GitHub信息
# 3. 运行脚本时会自动加载环境变量
# 4. 也可以直接在系统中设置环境变量：
#    export GITHUB_TOKEN=your-actual-token
#    export GITHUB_OWNER=your-actual-username
#    export GITHUB_REPO=your-actual-repo
