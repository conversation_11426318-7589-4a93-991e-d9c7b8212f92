{"rewrites": [{"source": "/", "destination": "/web/index.html"}, {"source": "/index.html", "destination": "/web/index.html"}, {"source": "/dashboard", "destination": "/web/index.html"}, {"source": "/api/data", "destination": "/web/assets/data/test_results.json"}, {"source": "/test_results.json", "destination": "/web/assets/data/test_results.json"}, {"source": "/history.json", "destination": "/web/assets/data/history.json"}, {"source": "/assets/(.*)", "destination": "/web/assets/$1"}], "headers": [{"source": "/(.*).json", "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Cache-Control", "value": "s-maxage=300, stale-while-revalidate"}]}, {"source": "/(.*).css", "headers": [{"key": "Content-Type", "value": "text/css"}, {"key": "Cache-Control", "value": "s-maxage=86400, stale-while-revalidate"}]}, {"source": "/(.*).js", "headers": [{"key": "Content-Type", "value": "application/javascript"}, {"key": "Cache-Control", "value": "s-maxage=86400, stale-while-revalidate"}]}]}