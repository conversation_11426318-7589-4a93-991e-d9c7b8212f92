/* Define CSS Layers for better organization */
@layer reset, base, components, utilities;

/* Modern CSS Reset and Base Styles */
@layer reset {
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
}

@layer base {
    :root {
        /* Color Palette */
        --color-primary: #2563eb;
        --color-primary-hover: #1d4ed8;
        --color-success: #10b981;
        --color-warning: #f59e0b;
        --color-danger: #ef4444;
        --color-gray-50: #f9fafb;
        --color-gray-100: #f3f4f6;
        --color-gray-200: #e5e7eb;
        --color-gray-400: #9ca3af;
        --color-gray-500: #6b7280;
        --color-gray-600: #4b5563;
        --color-gray-700: #374151;
        --color-gray-900: #111827;

        /* Spacing System */
        --spacing-2: 0.5rem;
        --spacing-3: 0.75rem;
        --spacing-4: 1rem;
        --spacing-6: 1.5rem;
        --spacing-8: 2rem;
        --spacing-12: 3rem;

        /* Typography */
        --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
        --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

        /* Shadows */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

        /* Border Radius */
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;

        /* Layout Dimensions */
        --url-item-height: 60px;
    }

    body {
        font-family: var(--font-family-sans);
        background-color: var(--color-gray-50);
        color: var(--color-gray-900);
        line-height: 1.6;
        min-height: 100vh;
    }
}

@layer utilities {
    /* Skip link for accessibility */
    .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--color-primary);
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
        font-weight: 600;
        transition: top 0.3s;
    }

    .skip-link:focus {
        top: 6px;
    }

    .screen-reader-text {
        clip: rect(1px, 1px, 1px, 1px);
        position: absolute !important;
        height: 1px;
        width: 1px;
        overflow: hidden;
    }

    .screen-reader-text:focus {
        background-color: var(--color-gray-100);
        border-radius: 3px;
        box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
        clip: auto !important;
        color: var(--color-gray-900);
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        height: auto;
        left: 5px;
        line-height: normal;
        padding: 15px 23px 14px;
        text-decoration: none;
        top: 5px;
        width: auto;
        z-index: 100000;
    }
}
